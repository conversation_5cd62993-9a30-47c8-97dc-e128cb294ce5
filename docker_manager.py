import docker
import json
import time
import uuid
from typing import Dict, Any, Optional
from repo_crawler import RepoCrawler


class DockerManager:
    """Manages Docker containers for repository crawling operations."""
    
    def __init__(self):
        try:
            self.client = docker.from_env()
            # Test connection
            self.client.ping()
        except Exception as e:
            raise Exception(f"Failed to connect to Docker daemon: {str(e)}")
    
    def create_crawler_container(self, github_url: str, include_hidden: bool = False) -> Dict[str, Any]:
        """Create a container to crawl a GitHub repository."""
        container_name = f"gitcrawl-{uuid.uuid4().hex[:8]}"
        
        try:
            # Use a minimal Python image for the crawler
            image = "python:3.11-alpine"
            
            # Prepare the command to run inside the container
            command = self._prepare_crawler_command(github_url, include_hidden)
            
            # Create and start the container
            container = self.client.containers.run(
                image=image,
                command=command,
                name=container_name,
                detach=True,
                remove=False,  # We'll remove it manually after getting results
                working_dir="/app",
                volumes={
                    # Mount a temporary directory for the repository
                    f"/tmp/gitcrawl-{container_name}": {"bind": "/tmp/repos", "mode": "rw"}
                },
                environment={
                    "PYTHONUNBUFFERED": "1"
                },
                network_mode="bridge"
            )
            
            return {
                "container_id": container.id,
                "container_name": container_name,
                "status": "created"
            }
            
        except Exception as e:
            raise Exception(f"Failed to create container: {str(e)}")
    
    def _prepare_crawler_command(self, github_url: str, include_hidden: bool) -> list:
        """Prepare the command to run the crawler inside the container."""
        # Install required packages and run the crawler
        install_cmd = "pip install gitpython requests"
        
        # Create a simple Python script to run inside the container
        crawler_script = f'''
import os
import json
import subprocess
import sys

# Install required packages
subprocess.check_call([sys.executable, "-m", "pip", "install", "gitpython", "requests"])

# Import after installation
from git import Repo
import shutil
import tempfile
from urllib.parse import urlparse

def crawl_repo():
    url = "{github_url}"
    include_hidden = {str(include_hidden).lower()}
    
    # Clone repository
    temp_dir = "/tmp/repos"
    os.makedirs(temp_dir, exist_ok=True)
    
    repo_name = url.split("/")[-1].replace(".git", "")
    clone_dir = os.path.join(temp_dir, repo_name)
    
    if os.path.exists(clone_dir):
        shutil.rmtree(clone_dir)
    
    try:
        Repo.clone_from(url, clone_dir, depth=1)
        
        def build_tree(path, relative=""):
            items = []
            try:
                for item in sorted(os.listdir(path)):
                    if not include_hidden and item.startswith("."):
                        continue
                    
                    item_path = os.path.join(path, item)
                    item_relative = os.path.join(relative, item) if relative else item
                    
                    if os.path.isdir(item_path):
                        items.append({{
                            "name": item,
                            "type": "directory", 
                            "path": item_relative.replace("\\\\", "/"),
                            "children": build_tree(item_path, item_relative)
                        }})
                    else:
                        stat = os.stat(item_path)
                        items.append({{
                            "name": item,
                            "type": "file",
                            "path": item_relative.replace("\\\\", "/"),
                            "size": stat.st_size
                        }})
            except PermissionError:
                pass
            return items
        
        tree = build_tree(clone_dir)
        result = {{
            "repository": {{
                "url": url,
                "name": repo_name
            }},
            "tree": {{
                "name": repo_name,
                "type": "directory",
                "path": "",
                "children": tree
            }}
        }}
        
        print("GITCRAWL_RESULT_START")
        print(json.dumps(result, indent=2))
        print("GITCRAWL_RESULT_END")
        
    except Exception as e:
        print(f"Error: {{str(e)}}")
        sys.exit(1)
    finally:
        if os.path.exists(clone_dir):
            shutil.rmtree(clone_dir)

if __name__ == "__main__":
    crawl_repo()
'''
        
        return ["python", "-c", crawler_script]
    
    def wait_for_container_completion(self, container_id: str, timeout: int = 300) -> Dict[str, Any]:
        """Wait for container to complete and return results."""
        try:
            container = self.client.containers.get(container_id)
            
            # Wait for container to finish
            result = container.wait(timeout=timeout)
            
            # Get logs
            logs = container.logs().decode('utf-8')
            
            # Parse the result from logs
            crawl_result = self._extract_result_from_logs(logs)
            
            return {
                "status": "completed",
                "exit_code": result["StatusCode"],
                "result": crawl_result,
                "logs": logs
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _extract_result_from_logs(self, logs: str) -> Optional[Dict[str, Any]]:
        """Extract JSON result from container logs."""
        try:
            start_marker = "GITCRAWL_RESULT_START"
            end_marker = "GITCRAWL_RESULT_END"
            
            start_idx = logs.find(start_marker)
            end_idx = logs.find(end_marker)
            
            if start_idx != -1 and end_idx != -1:
                json_str = logs[start_idx + len(start_marker):end_idx].strip()
                return json.loads(json_str)
            
            return None
        except Exception:
            return None
    
    def cleanup_container(self, container_id: str) -> bool:
        """Remove the container and cleanup resources."""
        try:
            container = self.client.containers.get(container_id)
            container.remove(force=True)
            return True
        except Exception:
            return False
    
    def crawl_repository_in_container(self, github_url: str, include_hidden: bool = False) -> Dict[str, Any]:
        """Complete workflow: create container, crawl repository, get results, cleanup."""
        container_info = None
        
        try:
            # Create container
            container_info = self.create_crawler_container(github_url, include_hidden)
            container_id = container_info["container_id"]
            
            # Wait for completion
            result = self.wait_for_container_completion(container_id)
            
            return result
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
        finally:
            # Cleanup
            if container_info:
                self.cleanup_container(container_info["container_id"])
