import logging
import traceback
from functools import wraps
from flask import jsonify, request
from typing import Dict, Any, Callable


logger = logging.getLogger(__name__)


class GitCrawlError(Exception):
    """Base exception for GitCrawl application."""
    
    def __init__(self, message: str, status_code: int = 500, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.details = details or {}


class ValidationError(GitCrawlError):
    """Exception for validation errors."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, 400, details)


class RepositoryError(GitCrawlError):
    """Exception for repository-related errors."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, 422, details)


class DockerError(GitCrawlError):
    """Exception for Docker-related errors."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, 503, details)


class NetworkError(GitCrawlError):
    """Exception for network-related errors."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, 502, details)


def handle_errors(f: Callable) -> Callable:
    """Decorator to handle errors in Flask routes."""
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        
        except ValidationError as e:
            logger.warning(f"Validation error in {f.__name__}: {e.message}")
            return jsonify({
                "error": e.message,
                "type": "validation_error",
                "details": e.details
            }), e.status_code
        
        except RepositoryError as e:
            logger.error(f"Repository error in {f.__name__}: {e.message}")
            return jsonify({
                "error": e.message,
                "type": "repository_error",
                "details": e.details
            }), e.status_code
        
        except DockerError as e:
            logger.error(f"Docker error in {f.__name__}: {e.message}")
            return jsonify({
                "error": e.message,
                "type": "docker_error",
                "details": e.details
            }), e.status_code
        
        except NetworkError as e:
            logger.error(f"Network error in {f.__name__}: {e.message}")
            return jsonify({
                "error": e.message,
                "type": "network_error",
                "details": e.details
            }), e.status_code
        
        except GitCrawlError as e:
            logger.error(f"GitCrawl error in {f.__name__}: {e.message}")
            return jsonify({
                "error": e.message,
                "type": "gitcrawl_error",
                "details": e.details
            }), e.status_code
        
        except Exception as e:
            logger.error(f"Unexpected error in {f.__name__}: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({
                "error": "Internal server error",
                "type": "internal_error",
                "details": {"original_error": str(e)} if logger.level <= logging.DEBUG else {}
            }), 500
    
    return decorated_function


def log_request_info():
    """Log request information for debugging."""
    if logger.level <= logging.DEBUG:
        logger.debug(f"Request: {request.method} {request.url}")
        logger.debug(f"Headers: {dict(request.headers)}")
        if request.is_json:
            logger.debug(f"JSON Body: {request.get_json()}")
        elif request.form:
            logger.debug(f"Form Data: {dict(request.form)}")


class ErrorReporter:
    """Utility class for error reporting and metrics."""
    
    def __init__(self):
        self.error_counts = {}
    
    def report_error(self, error_type: str, error_message: str, context: Dict[str, Any] = None):
        """Report an error for monitoring/metrics."""
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        logger.error(f"Error reported - Type: {error_type}, Message: {error_message}")
        if context:
            logger.error(f"Context: {context}")
    
    def get_error_stats(self) -> Dict[str, int]:
        """Get error statistics."""
        return self.error_counts.copy()
    
    def reset_stats(self):
        """Reset error statistics."""
        self.error_counts.clear()


# Global error reporter instance
error_reporter = ErrorReporter()


def create_error_response(message: str, error_type: str = "error", 
                         status_code: int = 500, details: Dict[str, Any] = None) -> tuple:
    """Create a standardized error response."""
    response_data = {
        "error": message,
        "type": error_type,
        "timestamp": None,  # Could add timestamp if needed
    }
    
    if details:
        response_data["details"] = details
    
    return jsonify(response_data), status_code


def validate_request_size(max_size_mb: int = 10):
    """Decorator to validate request size."""
    
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            content_length = request.content_length
            if content_length and content_length > max_size_mb * 1024 * 1024:
                raise ValidationError(
                    f"Request too large. Maximum size: {max_size_mb}MB",
                    {"max_size_mb": max_size_mb, "received_size_mb": content_length / (1024 * 1024)}
                )
            return f(*args, **kwargs)
        return decorated_function
    
    return decorator


def rate_limit_check(max_requests_per_minute: int = 60):
    """Simple rate limiting decorator (in-memory, not persistent)."""
    request_counts = {}
    
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            import time
            
            client_ip = request.remote_addr
            current_time = int(time.time() / 60)  # Current minute
            
            key = f"{client_ip}:{current_time}"
            
            if key in request_counts:
                if request_counts[key] >= max_requests_per_minute:
                    raise ValidationError(
                        "Rate limit exceeded",
                        {"max_requests_per_minute": max_requests_per_minute}
                    )
                request_counts[key] += 1
            else:
                request_counts[key] = 1
                
                # Cleanup old entries
                keys_to_remove = [k for k in request_counts.keys() 
                                if int(k.split(':')[1]) < current_time - 5]
                for k in keys_to_remove:
                    del request_counts[k]
            
            return f(*args, **kwargs)
        return decorated_function
    
    return decorator
