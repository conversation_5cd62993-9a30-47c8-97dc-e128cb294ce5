import re
import requests
from urllib.parse import urlparse
from typing import Dict, Any, Optional


class GitHubValidator:
    """Validator for GitHub URLs and repository accessibility."""
    
    @staticmethod
    def validate_url_format(url: str) -> bool:
        """Validate GitHub URL format."""
        if not url:
            return False
        
        # Remove trailing slash and .git extension
        clean_url = url.rstrip('/').replace('.git', '')
        
        # GitHub URL patterns
        patterns = [
            r'^https://github\.com/[\w\-\.]+/[\w\-\.]+$',
            r'^git@github\.com:[\w\-\.]+/[\w\-\.]+$'
        ]
        
        return any(re.match(pattern, clean_url) for pattern in patterns)
    
    @staticmethod
    def extract_repo_parts(url: str) -> Optional[Dict[str, str]]:
        """Extract owner and repository name from GitHub URL."""
        try:
            # Handle different URL formats
            if url.startswith('git@'):
                # SSH format: **************:owner/repo.git
                parts = url.replace('**************:', '').replace('.git', '').split('/')
            else:
                # HTTPS format: https://github.com/owner/repo
                parsed = urlparse(url.rstrip('/').replace('.git', ''))
                parts = parsed.path.strip('/').split('/')
            
            if len(parts) >= 2:
                return {
                    'owner': parts[0],
                    'repo': parts[1],
                    'full_name': f"{parts[0]}/{parts[1]}"
                }
            return None
        except Exception:
            return None
    
    @staticmethod
    def check_repository_accessibility(url: str, timeout: int = 10) -> Dict[str, Any]:
        """Check if the repository is publicly accessible."""
        try:
            repo_parts = GitHubValidator.extract_repo_parts(url)
            if not repo_parts:
                return {"accessible": False, "error": "Invalid URL format"}
            
            # Convert to HTTPS format for API check
            api_url = f"https://api.github.com/repos/{repo_parts['full_name']}"
            
            response = requests.get(api_url, timeout=timeout)
            
            if response.status_code == 200:
                repo_data = response.json()
                return {
                    "accessible": True,
                    "private": repo_data.get("private", False),
                    "size": repo_data.get("size", 0),
                    "default_branch": repo_data.get("default_branch", "main"),
                    "language": repo_data.get("language"),
                    "description": repo_data.get("description")
                }
            elif response.status_code == 404:
                return {"accessible": False, "error": "Repository not found or private"}
            elif response.status_code == 403:
                return {"accessible": False, "error": "API rate limit exceeded"}
            else:
                return {"accessible": False, "error": f"HTTP {response.status_code}"}
                
        except requests.RequestException as e:
            return {"accessible": False, "error": f"Network error: {str(e)}"}
        except Exception as e:
            return {"accessible": False, "error": f"Validation error: {str(e)}"}


class InputValidator:
    """General input validation utilities."""
    
    @staticmethod
    def validate_crawl_request(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate crawl request data."""
        errors = []
        
        # Check required fields
        if 'url' not in data:
            errors.append("Missing required field: url")
        
        # Validate URL if present
        if 'url' in data:
            if not isinstance(data['url'], str):
                errors.append("URL must be a string")
            elif not GitHubValidator.validate_url_format(data['url']):
                errors.append("Invalid GitHub URL format")
        
        # Validate optional fields
        if 'include_hidden' in data and not isinstance(data['include_hidden'], bool):
            errors.append("include_hidden must be a boolean")
        
        if 'use_docker' in data and not isinstance(data['use_docker'], bool):
            errors.append("use_docker must be a boolean")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    @staticmethod
    def sanitize_url(url: str) -> str:
        """Sanitize and normalize GitHub URL."""
        if not url:
            return url
        
        # Remove whitespace
        url = url.strip()
        
        # Convert SSH to HTTPS
        if url.startswith('**************:'):
            url = url.replace('**************:', 'https://github.com/')
        
        # Remove .git extension
        if url.endswith('.git'):
            url = url[:-4]
        
        # Remove trailing slash
        url = url.rstrip('/')
        
        return url


class SecurityValidator:
    """Security-related validation utilities."""
    
    @staticmethod
    def is_safe_repository_size(size_kb: int, max_size_mb: int = 500) -> bool:
        """Check if repository size is within safe limits."""
        max_size_kb = max_size_mb * 1024
        return size_kb <= max_size_kb
    
    @staticmethod
    def validate_file_path(file_path: str) -> bool:
        """Validate file path for security issues."""
        # Check for path traversal attempts
        dangerous_patterns = ['../', '..\\', '/etc/', '/proc/', '/sys/']
        
        for pattern in dangerous_patterns:
            if pattern in file_path:
                return False
        
        return True
    
    @staticmethod
    def is_allowed_file_extension(filename: str) -> bool:
        """Check if file extension is allowed for processing."""
        # Define allowed extensions (can be expanded)
        allowed_extensions = {
            # Code files
            '.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.cs', '.php',
            '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.clj', '.hs',
            # Web files
            '.html', '.css', '.scss', '.sass', '.less', '.vue', '.jsx', '.tsx',
            # Config files
            '.json', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf',
            # Documentation
            '.md', '.rst', '.txt', '.doc', '.docx',
            # Data files
            '.csv', '.xml', '.sql',
            # Build files
            '.dockerfile', '.makefile', '.gradle', '.maven'
        }
        
        # Get file extension
        ext = '.' + filename.split('.')[-1].lower() if '.' in filename else ''
        
        # Special cases
        special_files = {
            'dockerfile', 'makefile', 'readme', 'license', 'changelog',
            'contributing', 'code_of_conduct', 'security'
        }
        
        filename_lower = filename.lower()
        
        return (ext in allowed_extensions or 
                any(special in filename_lower for special in special_files) or
                not ext)  # Files without extensions are usually allowed
