import os
import json
import shutil
import tempfile
import re
from urllib.parse import urlparse
from pathlib import Path
from typing import Dict, List, Optional, Any
import git
from git import Repo


class RepoCrawler:
    """Service for crawling GitHub repositories and generating directory trees."""
    
    def __init__(self):
        self.temp_dir = "/tmp/repos"
        os.makedirs(self.temp_dir, exist_ok=True)
    
    def validate_github_url(self, url: str) -> bool:
        """Validate if the provided URL is a valid GitHub repository URL."""
        github_pattern = r'^https://github\.com/[\w\-\.]+/[\w\-\.]+/?$'
        return bool(re.match(github_pattern, url.rstrip('/')))
    
    def extract_repo_info(self, url: str) -> Dict[str, str]:
        """Extract owner and repository name from GitHub URL."""
        parsed = urlparse(url.rstrip('/'))
        path_parts = parsed.path.strip('/').split('/')
        
        if len(path_parts) >= 2:
            return {
                'owner': path_parts[0],
                'repo': path_parts[1],
                'full_name': f"{path_parts[0]}/{path_parts[1]}"
            }
        raise ValueError("Invalid GitHub URL format")
    
    def clone_repository(self, url: str) -> str:
        """Clone the repository to a temporary directory."""
        repo_info = self.extract_repo_info(url)
        repo_name = repo_info['repo']
        
        # Create unique directory name
        clone_dir = os.path.join(self.temp_dir, f"{repo_name}_{os.getpid()}")
        
        # Remove directory if it exists
        if os.path.exists(clone_dir):
            shutil.rmtree(clone_dir)
        
        try:
            # Clone the repository
            Repo.clone_from(url, clone_dir, depth=1)
            return clone_dir
        except Exception as e:
            if os.path.exists(clone_dir):
                shutil.rmtree(clone_dir)
            raise Exception(f"Failed to clone repository: {str(e)}")
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get information about a file."""
        stat = os.stat(file_path)
        return {
            'size': stat.st_size,
            'modified': stat.st_mtime,
            'type': 'file'
        }
    
    def should_ignore_path(self, path: str) -> bool:
        """Check if a path should be ignored (common ignore patterns)."""
        ignore_patterns = [
            '.git', '.gitignore', '.gitmodules',
            '__pycache__', '.pyc', '.pyo',
            'node_modules', '.npm',
            '.DS_Store', 'Thumbs.db',
            '.vscode', '.idea',
            '*.log', '*.tmp'
        ]
        
        path_name = os.path.basename(path)
        for pattern in ignore_patterns:
            if pattern.startswith('*'):
                if path_name.endswith(pattern[1:]):
                    return True
            elif pattern in path_name or path_name == pattern:
                return True
        return False
    
    def generate_tree(self, directory: str, include_hidden: bool = False) -> Dict[str, Any]:
        """Generate a tree structure of the directory."""
        def build_tree(current_path: str, relative_path: str = "") -> Dict[str, Any]:
            items = []
            
            try:
                for item in sorted(os.listdir(current_path)):
                    if not include_hidden and item.startswith('.'):
                        continue
                    
                    item_path = os.path.join(current_path, item)
                    item_relative = os.path.join(relative_path, item) if relative_path else item
                    
                    if self.should_ignore_path(item_path):
                        continue
                    
                    if os.path.isdir(item_path):
                        dir_info = {
                            'name': item,
                            'type': 'directory',
                            'path': item_relative.replace('\\', '/'),
                            'children': build_tree(item_path, item_relative)['children']
                        }
                        items.append(dir_info)
                    else:
                        file_info = self.get_file_info(item_path)
                        file_info.update({
                            'name': item,
                            'path': item_relative.replace('\\', '/'),
                        })
                        items.append(file_info)
            
            except PermissionError:
                pass  # Skip directories we can't read
            
            return {'children': items}
        
        root_name = os.path.basename(directory)
        tree = build_tree(directory)
        
        return {
            'name': root_name,
            'type': 'directory',
            'path': '',
            'children': tree['children']
        }
    
    def crawl_repository(self, github_url: str, include_hidden: bool = False) -> Dict[str, Any]:
        """Main method to crawl a GitHub repository and return its tree structure."""
        if not self.validate_github_url(github_url):
            raise ValueError("Invalid GitHub URL provided")
        
        repo_info = self.extract_repo_info(github_url)
        clone_dir = None
        
        try:
            # Clone the repository
            clone_dir = self.clone_repository(github_url)
            
            # Generate tree structure
            tree = self.generate_tree(clone_dir, include_hidden)
            
            # Add repository metadata
            result = {
                'repository': {
                    'url': github_url,
                    'owner': repo_info['owner'],
                    'name': repo_info['repo'],
                    'full_name': repo_info['full_name']
                },
                'tree': tree,
                'metadata': {
                    'total_files': self._count_files(tree),
                    'total_directories': self._count_directories(tree)
                }
            }
            
            return result
            
        finally:
            # Cleanup: remove cloned directory
            if clone_dir and os.path.exists(clone_dir):
                shutil.rmtree(clone_dir)
    
    def _count_files(self, tree: Dict[str, Any]) -> int:
        """Count total number of files in the tree."""
        count = 0
        if 'children' in tree:
            for child in tree['children']:
                if child['type'] == 'file':
                    count += 1
                elif child['type'] == 'directory':
                    count += self._count_files(child)
        return count
    
    def _count_directories(self, tree: Dict[str, Any]) -> int:
        """Count total number of directories in the tree."""
        count = 0
        if 'children' in tree:
            for child in tree['children']:
                if child['type'] == 'directory':
                    count += 1 + self._count_directories(child)
        return count
