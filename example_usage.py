#!/usr/bin/env python3
"""
Example usage of the GitCrawl API.
This script demonstrates how to use the GitCrawl service to crawl GitHub repositories.
"""

import requests
import json
import time
from typing import Dict, Any


class GitCrawlClient:
    """Client for interacting with the GitCrawl API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
    
    def health_check(self) -> Dict[str, Any]:
        """Check if the service is healthy."""
        try:
            response = requests.get(f"{self.base_url}/")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": str(e)}
    
    def crawl_repository(self, github_url: str, include_hidden: bool = False, 
                        use_docker: bool = True) -> Dict[str, Any]:
        """Crawl a GitHub repository using POST endpoint."""
        try:
            payload = {
                "url": github_url,
                "include_hidden": include_hidden,
                "use_docker": use_docker
            }
            
            response = requests.post(
                f"{self.base_url}/crawl",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": str(e)}
    
    def crawl_repository_simple(self, github_url: str, include_hidden: bool = False,
                               use_docker: bool = True) -> Dict[str, Any]:
        """Crawl a GitHub repository using GET endpoint."""
        try:
            params = {
                "url": github_url,
                "include_hidden": str(include_hidden).lower(),
                "use_docker": str(use_docker).lower()
            }
            
            response = requests.get(f"{self.base_url}/crawl/simple", params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": str(e)}


def print_tree_structure(tree: Dict[str, Any], indent: int = 0):
    """Print the tree structure in a readable format."""
    prefix = "  " * indent
    
    if tree["type"] == "directory":
        print(f"{prefix}📁 {tree['name']}/")
        if "children" in tree:
            for child in tree["children"]:
                print_tree_structure(child, indent + 1)
    else:
        size_str = f" ({tree.get('size', 0)} bytes)" if 'size' in tree else ""
        print(f"{prefix}📄 {tree['name']}{size_str}")


def analyze_repository(result: Dict[str, Any]):
    """Analyze and display repository information."""
    if "error" in result:
        print(f"❌ Error: {result['error']}")
        return
    
    repo_info = result.get("repository", {})
    metadata = result.get("metadata", {})
    tree = result.get("tree", {})
    
    print("=" * 60)
    print(f"📦 Repository: {repo_info.get('full_name', 'Unknown')}")
    print(f"🔗 URL: {repo_info.get('url', 'Unknown')}")
    print(f"📊 Total Files: {metadata.get('total_files', 0)}")
    print(f"📁 Total Directories: {metadata.get('total_directories', 0)}")
    print("=" * 60)
    print("\n🌳 Directory Structure:")
    print_tree_structure(tree)
    print("=" * 60)


def main():
    """Main example function."""
    # Initialize client
    client = GitCrawlClient()
    
    # Check service health
    print("🔍 Checking service health...")
    health = client.health_check()
    if "error" in health:
        print(f"❌ Service not available: {health['error']}")
        return
    
    print(f"✅ Service is healthy: {health.get('service', 'Unknown')} v{health.get('version', 'Unknown')}")
    print(f"🐳 Docker available: {health.get('docker_available', False)}")
    print()
    
    # Example repositories to crawl
    repositories = [
        "https://github.com/octocat/Hello-World",
        "https://github.com/microsoft/vscode",  # Larger repository
        "https://github.com/python/cpython"     # Very large repository
    ]
    
    for repo_url in repositories:
        print(f"\n🚀 Crawling repository: {repo_url}")
        
        # Start timing
        start_time = time.time()
        
        # Crawl using POST endpoint
        result = client.crawl_repository(repo_url, include_hidden=False, use_docker=True)
        
        # End timing
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  Crawling took {duration:.2f} seconds")
        
        # Analyze results
        analyze_repository(result)
        
        # Wait a bit between requests to be nice to the service
        time.sleep(1)


def interactive_mode():
    """Interactive mode for testing different repositories."""
    client = GitCrawlClient()
    
    print("🎯 GitCrawl Interactive Mode")
    print("Enter GitHub repository URLs to crawl (or 'quit' to exit)")
    print("Example: https://github.com/octocat/Hello-World")
    print()
    
    while True:
        try:
            url = input("GitHub URL: ").strip()
            
            if url.lower() in ['quit', 'exit', 'q']:
                break
            
            if not url:
                continue
            
            # Ask for options
            include_hidden = input("Include hidden files? (y/N): ").lower().startswith('y')
            use_docker = input("Use Docker? (Y/n): ").lower() != 'n'
            
            print(f"\n🚀 Crawling {url}...")
            start_time = time.time()
            
            result = client.crawl_repository(url, include_hidden, use_docker)
            
            end_time = time.time()
            print(f"⏱️  Completed in {end_time - start_time:.2f} seconds")
            
            analyze_repository(result)
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_mode()
    else:
        main()
