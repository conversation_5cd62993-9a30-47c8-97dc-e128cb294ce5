from flask import Flask, request, jsonify
import os
import logging
from repo_crawler import Repo<PERSON>rawler
from docker_manager import DockerManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Initialize services
crawler = RepoCrawler()
docker_manager = None

# Try to initialize Docker manager, fallback to direct crawler if Docker is not available
try:
    docker_manager = DockerManager()
    logger.info("Docker manager initialized successfully")
except Exception as e:
    logger.warning(f"Docker not available, using direct crawler: {str(e)}")


@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "service": "GitCrawl",
        "status": "healthy",
        "version": "1.0.0",
        "docker_available": docker_manager is not None
    })


@app.route('/crawl', methods=['POST'])
def crawl_repository():
    """
    Crawl a GitHub repository and return its directory tree structure.
    
    Expected JSON payload:
    {
        "url": "https://github.com/owner/repo",
        "include_hidden": false,  // optional, default: false
        "use_docker": true        // optional, default: true if available
    }
    """
    try:
        # Validate request
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400
        
        data = request.get_json()
        
        # Validate required fields
        if 'url' not in data:
            return jsonify({"error": "Missing required field: url"}), 400
        
        github_url = data['url']
        include_hidden = data.get('include_hidden', False)
        use_docker = data.get('use_docker', True)
        
        # Validate URL
        if not crawler.validate_github_url(github_url):
            return jsonify({"error": "Invalid GitHub URL format"}), 400
        
        logger.info(f"Crawling repository: {github_url}")
        
        # Choose crawling method
        if use_docker and docker_manager:
            logger.info("Using Docker container for crawling")
            result = docker_manager.crawl_repository_in_container(github_url, include_hidden)
            
            if result.get("status") == "error":
                return jsonify({"error": result.get("error", "Unknown error")}), 500
            
            if result.get("status") == "completed" and result.get("result"):
                return jsonify(result["result"])
            else:
                return jsonify({"error": "Failed to get results from container"}), 500
        
        else:
            logger.info("Using direct crawler")
            result = crawler.crawl_repository(github_url, include_hidden)
            return jsonify(result)
    
    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        return jsonify({"error": str(e)}), 400
    
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@app.route('/crawl/simple', methods=['GET'])
def crawl_repository_simple():
    """
    Simple GET endpoint for crawling repositories.
    
    Query parameters:
    - url: GitHub repository URL (required)
    - include_hidden: Include hidden files (optional, default: false)
    - use_docker: Use Docker container (optional, default: true if available)
    """
    try:
        github_url = request.args.get('url')
        if not github_url:
            return jsonify({"error": "Missing required parameter: url"}), 400
        
        include_hidden = request.args.get('include_hidden', 'false').lower() == 'true'
        use_docker = request.args.get('use_docker', 'true').lower() == 'true'
        
        # Validate URL
        if not crawler.validate_github_url(github_url):
            return jsonify({"error": "Invalid GitHub URL format"}), 400
        
        logger.info(f"Crawling repository (simple): {github_url}")
        
        # Choose crawling method
        if use_docker and docker_manager:
            result = docker_manager.crawl_repository_in_container(github_url, include_hidden)
            
            if result.get("status") == "error":
                return jsonify({"error": result.get("error", "Unknown error")}), 500
            
            if result.get("status") == "completed" and result.get("result"):
                return jsonify(result["result"])
            else:
                return jsonify({"error": "Failed to get results from container"}), 500
        
        else:
            result = crawler.crawl_repository(github_url, include_hidden)
            return jsonify(result)
    
    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        return jsonify({"error": str(e)}), 400
    
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@app.route('/api/docs', methods=['GET'])
def api_documentation():
    """API documentation endpoint."""
    docs = {
        "GitCrawl API": {
            "version": "1.0.0",
            "description": "API for crawling GitHub repositories and returning directory tree structures",
            "endpoints": {
                "GET /": {
                    "description": "Health check endpoint",
                    "response": "Service status and configuration"
                },
                "POST /crawl": {
                    "description": "Crawl a GitHub repository",
                    "payload": {
                        "url": "GitHub repository URL (required)",
                        "include_hidden": "Include hidden files (optional, default: false)",
                        "use_docker": "Use Docker container (optional, default: true)"
                    },
                    "response": "Repository tree structure in JSON format"
                },
                "GET /crawl/simple": {
                    "description": "Simple GET endpoint for crawling",
                    "parameters": {
                        "url": "GitHub repository URL (required)",
                        "include_hidden": "Include hidden files (optional, default: false)",
                        "use_docker": "Use Docker container (optional, default: true)"
                    },
                    "response": "Repository tree structure in JSON format"
                },
                "GET /api/docs": {
                    "description": "This documentation endpoint"
                }
            },
            "example_usage": {
                "curl_post": "curl -X POST http://localhost:8000/crawl -H 'Content-Type: application/json' -d '{\"url\": \"https://github.com/octocat/Hello-World\"}'",
                "curl_get": "curl 'http://localhost:8000/crawl/simple?url=https://github.com/octocat/Hello-World'"
            }
        }
    }
    return jsonify(docs)


@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Endpoint not found"}), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "Internal server error"}), 500


if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    logger.info(f"Starting GitCrawl service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
